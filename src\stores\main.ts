import { defineStore } from 'pinia';
import type { Book } from '../types/book';
import type { Chapter } from '../types/chapter';
import type { Item } from '../types/item';

interface State {
  books: Book[];
  selectedBookId: string | null;
  loading: boolean;
  error: string | null;
}

export const useMainStore = defineStore('main', {
  state: (): State => ({
    books: [],
    selectedBookId: null,
    loading: false,
    error: null,
  }),

  actions: {
    async loadBooks() {
      this.loading = true;
      this.error = null;
      try {
        const response = await fetch('/books.json');
        if (!response.ok) {
          throw new Error(`Failed to load books: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log('Raw books.json data:', data);
        let booksArray = Array.isArray(data) ? data : Array.isArray(data.books) ? data.books : [];
        if (!Array.isArray(booksArray)) {
          throw new Error('Invalid books data: Expected an array or { books: [...] }');
        }
        this.books = booksArray;
      } catch (error) {
        this.error = String(error);
        this.books = [];
        console.error('Error loading books:', error);
      } finally {
        this.loading = false;
      }
    },

    async addBook(book: Omit<Book, 'chapters'> & { chapters: Chapter[] }) {
      this.books.push(book);
      try {
        const updatedBooks = this.books;
        console.log('Simulated saving to books.json:', JSON.stringify(updatedBooks, null, 2));
      } catch (error) {
        console.error('Error saving books:', error);
      }
    },

    selectBook(id: string | null) {
      this.selectedBookId = id;
    },

    addChapter(bookId: string, chapter: Chapter) {
      const book = this.books.find(b => b.id === bookId);
      if (book) {
        book.chapters.push(chapter);
      }
    },

    addItem(bookId: string, chapterId: string, item: Item) {
      const book = this.books.find(b => b.id === bookId);
      const chapter = book?.chapters.find(c => c.id === chapterId);
      if (chapter) {
        chapter.items.push(item);
      }
    },

    getBook(id: string | undefined): Book | undefined {
      return this.books.find(book => book.id === id);
    },
  },
});