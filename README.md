# Textbook Platform Demo

A Vue.js application for managing textbooks with QR code generation for items. This demo interface allows you to create books, chapters, and items, with automatic QR code generation for each item.

## Features

- ✅ **Book Management**: Add and view textbooks
- ✅ **Chapter Management**: Add chapters to books
- ✅ **Item Management**: Add different types of items (questions, text, images, links)
- ✅ **QR Code Generation**: Automatic QR code generation for each item
- ✅ **Stylized QR Codes**: Multiple color themes and styling options
- ✅ **File Persistence**: Changes are saved to `public/books.json`
- ✅ **Download QR Codes**: Download QR codes as PNG files

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm

### Installation

1. Clone or download the project
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Application

#### Option 1: Run Frontend and Backend Together (Recommended)
```bash
npm run dev:full
```

This will start:
- Backend server on `http://localhost:3001`
- Frontend development server on `http://localhost:5173`

#### Option 2: Run Separately

**Terminal 1 - Backend:**
```bash
npm run server
```

**Terminal 2 - Frontend:**
```bash
npm run dev
```

### Usage

1. **Adding Books**: Click the "+" button next to "Books" in the sidebar
2. **Adding Chapters**: Select a book, then click the "+" button next to "Chapters & Items"
3. **Adding Items**: Select a chapter, then fill out the item form on the right
4. **Viewing QR Codes**: 
   - After creating an item, a QR code will automatically appear
   - Click on existing items in the tree to view their QR codes
   - Choose different styling themes from the dropdown
   - Download QR codes using the "Download QR Code" button

### QR Code Styles

The application supports multiple QR code styles:
- **Default (Gray)**: Clean gray theme
- **Blue Theme**: Professional blue styling
- **Green Theme**: Nature-inspired green
- **Purple Theme**: Creative purple design
- **High Contrast**: Maximum readability black/white

### File Structure

```
├── src/
│   ├── components/
│   │   ├── common/
│   │   │   ├── ItemForm.vue          # Form for creating items
│   │   │   └── QRCodeDisplay.vue     # QR code generation and display
│   │   ├── layout/
│   │   │   └── Sidebar.vue           # Navigation sidebar
│   │   └── views/
│   │       └── BookDetails.vue       # Main content area
│   ├── stores/
│   │   └── main.ts                   # Pinia store for state management
│   └── types/                        # TypeScript type definitions
├── public/
│   └── books.json                    # Data persistence file
└── server.js                         # Express backend server
```

### API Endpoints

The backend provides the following endpoints:

- `GET /api/books` - Get all books
- `POST /api/books` - Add a new book
- `POST /api/books/:bookId/chapters` - Add a chapter to a book
- `POST /api/books/:bookId/chapters/:chapterId/items` - Add an item to a chapter
- `GET /api/qr/:bookId/:chapterId/:itemId` - Generate basic QR code
- `GET /api/qr-styled/:bookId/:chapterId/:itemId?style=<style>` - Generate styled QR code

### QR Code Data Format

Each QR code contains JSON data with:
```json
{
  "bookId": "string",
  "chapterId": "string", 
  "itemId": "string",
  "bookTitle": "string",
  "chapterTitle": "string",
  "itemTitle": "string",
  "itemType": "question|text|image|link",
  "url": "string"
}
```

### Future Firebase Integration

This demo is designed to be easily migrated to Firebase:
- The store methods are already async and can be updated to use Firebase APIs
- The data structure is compatible with Firestore
- QR code generation can be moved to Firebase Functions

### Development

- Built with Vue 3 + TypeScript
- Uses Pinia for state management
- PrimeVue for UI components
- Tailwind CSS for styling
- Express.js for backend API
- QRCode library for QR generation

### Notes

- The application automatically falls back to local state if the backend is not available
- QR codes are generated server-side for better performance and customization
- All changes are persisted to the `books.json` file in real-time
