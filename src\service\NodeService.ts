import type { TreeNode } from 'primevue/treenode';
import type { Book } from '../types/book';

export const NodeService = {
  getTreeNodes(book: Book): Promise<TreeNode[]> {
    return Promise.resolve(
      book.chapters.map(chapter => ({
        key: chapter.id,
        label: chapter.title,
        data: `Chapter: ${chapter.title}`,
        icon: 'pi pi-fw pi-book',
        selectable: true,
        children: chapter.items.map(item => ({
          key: item.id,
          label: item.title,
          data: `Item: ${item.type}`,
          icon: item.type === 'question' ? 'pi pi-fw pi-question-circle' : 'pi pi-fw pi-file',
          selectable: true,
        })),
      }))
    );
  },
};