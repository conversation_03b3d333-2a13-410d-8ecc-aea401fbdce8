<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-bold">Books</h2>
      <Button v-tooltip="'Create Book'" size="small" icon="pi pi-plus" @click="openAddBookDialog" />
    </div>
    <div v-if="store.loading" class="text-gray-500">Loading books...</div>
    <div v-else-if="store.error" class="text-red-500">Error: {{ store.error }}</div>
    <div v-else-if="!store.books.length" class="text-gray-500">No books available</div>
    <Menu v-else :model="menuItems" class="w-full">
      <template #submenulabel="{ item }">
        <span class="text-primary font-bold p-2">{{ item.label }}</span>
      </template>
      <template #item="{ item, props }">
        <a
          v-if="item.id"
          v-bind="props.action"
          :class="['flex items-center p-2 cursor-pointer', { 'selected-book': store.selectedBookId === item.id }]"
          @click="selectBook(item.id)"
        >
          <span>{{ item.label }}</span>
        </a>
      </template>
    </Menu>
    <Dialog v-model:visible="showAddBookDialog" header="Add Book" :style="{ width: '25vw' }">
      <InputText v-model="newBookTitle" placeholder="Book Title" class="w-full mb-2" />
      <InputText v-model="newBookDescription" placeholder="Description" class="w-full" />
      <template #footer>
        <Button label="Cancel" class="p-button-text" @click="showAddBookDialog = false" />
        <Button label="Save" @click="saveBook" />
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMainStore } from '../../stores/main';
import type { MenuItem } from 'primevue/menuitem';
import Button from 'primevue/button';
import Menu from 'primevue/menu';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';

const store = useMainStore();
const router = useRouter();
const route = useRoute();
const showAddBookDialog = ref(false);
const newBookTitle = ref('');
const newBookDescription = ref('');

// Function to handle book selection
const selectBook = (bookId: string) => {
  store.selectBook(bookId);
  router.push(`/book/${bookId}`);
};

const menuItems = computed<MenuItem[]>(() => {
  if (!Array.isArray(store.books)) {
    return [];
  }
  return [
    {
      label: 'Books',
      items: store.books.map(book => ({
        label: book.title,
        id: book.id,
      })),
    },
  ];
});

// Sync selectedBookId with route
watch(
  () => route.params.id,
  (id) => {
    if (typeof id === 'string') {
      store.selectBook(id);
    } else {
      store.selectBook(null);
    }
  },
  { immediate: true }
);

// Load books
store.loadBooks();

const openAddBookDialog = () => {
  newBookTitle.value = '';
  newBookDescription.value = '';
  showAddBookDialog.value = true;
};

const saveBook = () => {
  if (newBookTitle.value) {
    store.addBook({
      id: Date.now().toString(),
      title: newBookTitle.value,
      description: newBookDescription.value,
      chapters: [],
    });
    showAddBookDialog.value = false;
  }
};
</script>

<style scoped>
.selected-book {
  background-color: var(--p-primary-100);
  color: var(--p-primary-700);
  font-weight: 500;
}
</style>