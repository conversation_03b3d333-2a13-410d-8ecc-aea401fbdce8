<template>
  <div class="p-4 rounded">
    <div class="p-field mb-4">
      <label for="itemType">Item Type</label>
      <Select
        id="itemType"
        v-model="item.type"
        :options="itemTypes"
        placeholder="Select Item Type"
        class="w-full"
      />
    </div>
    <div v-if="item.type === 'question'">
      <InputText v-model="item.question" placeholder="Question" class="w-full mb-2" />
      <div v-for="(option, index) in item.options" :key="index" class="flex items-center mb-2">
        <InputText v-model="option.text" placeholder="Option" class="flex-1 mr-2" />
        <RadioButton :value="index" v-model="item.correctAnswer" />
      </div>
      <Button label="Add Option" icon="pi pi-plus" @click="addOption" class="mb-2" />
    </div>
    <div v-else-if="item.type === 'text'">
      <Textarea v-model="item.content" placeholder="Text Content" rows="5" class="w-full" />
    </div>
    <div v-else-if="item.type === 'image'">
      <FileUpload mode="basic" accept="image/*" disabled class="mb-2" />
      <small>Image upload preview only (no backend)</small>
    </div>
    <div v-else-if="item.type === 'link'">
      <InputText v-model="item.url" placeholder="URL" class="w-full" />
    </div>
    <div class="mt-4">
      <Button label="Save" @click="saveItem" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useMainStore } from '../../stores/main';
import type { Item } from '../../types/item';
import Select  from 'primevue/select';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import FileUpload from 'primevue/fileupload';
import RadioButton from 'primevue/radiobutton';
import Button from 'primevue/button';

interface Props {
  chapterId?: string;
}

const props = defineProps<Props>();
const store = useMainStore();
const route = useRoute();
const item = ref<Partial<Item>>({ type: undefined, options: [], correctAnswer: undefined });
const itemTypes = ['question', 'text', 'image', 'link'];

watch(() => props.chapterId, () => {
  item.value = { type: undefined, options: [], correctAnswer: undefined };
});

const addOption = () => {
  item.value.options = [...(item.value.options || []), { text: '' }];
};

const saveItem = () => {
  if (props.chapterId && item.value.type && route.params.id) {
    const newItem: Item = { ...item.value, id: Date.now().toString() } as Item;
    store.addItem(route.params.id as string, props.chapterId, newItem);
    item.value = { type: undefined, options: [], correctAnswer: undefined };
  }
};
</script>