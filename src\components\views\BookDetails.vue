<template>
  <div>
    <div v-if="!book">
      <h1 class="text-2xl font-bold mb-4">No Book Selected</h1>
      <p class="text-gray-500">Please select a book from the sidebar to view its details.</p>
    </div>
    <div v-else>
      <div class="flex gap-4">
        <div class="w-1/3">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-bold">Chapters & Items</h2>
            <Button v-tooltip="'Add Chapter'" size="small" icon="pi pi-plus" @click="openAddChapterDialog" />
          </div>
          <Tree
            :value="treeData"
            selectionMode="single"
            v-model:selectionKeys="selectedKey"
            @node-select="onNodeSelect"
            class="border border-surface-300 rounded-lg"
          />
        </div>
        <div class="w-2/3">
          <div v-if="selectedItemId">
            <h2 class="text-lg font-bold mb-2">Item QR Code</h2>
            <QRCodeDisplay
              :bookId="book!.id"
              :chapterId="selectedChapterId!"
              :itemId="selectedItemId"
            />
          </div>
          <div v-else-if="selectedChapterId">
            <h2 class="text-lg font-bold mb-2">Add Item</h2>
            <ItemForm :chapterId="selectedChapterId" />
          </div>
          <p v-else class="text-gray-500">Select a chapter or item to add items.</p>
        </div>
      </div>
      <Dialog v-model:visible="showAddChapterDialog" header="Add Chapter" :style="{ width: '25vw' }">
        <InputText v-model="newChapterTitle" placeholder="Chapter Title" class="w-full" />
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showAddChapterDialog = false" />
          <Button label="Save" @click="saveChapter" />
        </template>
      </Dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useMainStore } from '../../stores/main';
import type { Book } from '../../types/book';
import type { TreeNode } from 'primevue/treenode';
import ItemForm from '../common/ItemForm.vue';
import QRCodeDisplay from '../common/QRCodeDisplay.vue';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import Tree from 'primevue/tree';
import InputText from 'primevue/inputtext';
import { NodeService } from '../../service/NodeService';
import type { TreeSelectionKeys } from 'primevue/tree';

const store = useMainStore();
const route = useRoute();
const book = computed<Book | undefined>(() => store.getBook(route.params.id as string));
const selectedKey = ref<TreeSelectionKeys | undefined>(undefined);
const selectedChapterId = ref<string | null>(null);
const selectedItemId = ref<string | null>(null);
const showAddChapterDialog = ref(false);
const newChapterTitle = ref('');
const treeData = ref<TreeNode[]>([]);

// Load tree data when book changes
watch(
  book,
  async (newBook) => {
    if (newBook) {
      treeData.value = await NodeService.getTreeNodes(newBook);
    } else {
      treeData.value = [];
    }
  },
  { immediate: true }
);

const onNodeSelect = (node: TreeNode) => {
  if (!book.value || !node.key) {
    selectedKey.value = undefined;
    selectedChapterId.value = null;
    selectedItemId.value = null;
    return;
  }

  selectedKey.value = { [node.key]: { checked: true } };

  if (node.children) {
    // Chapter (has children)
    selectedChapterId.value = node.key;
    selectedItemId.value = null;
  } else {
    // Item (no children)
    const chapter = book.value.chapters.find(ch =>
      ch.items.some(item => item.id === node.key)
    );
    selectedChapterId.value = chapter?.id ?? null;
    selectedItemId.value = node.key;
  }
};

const openAddChapterDialog = () => {
  newChapterTitle.value = '';
  showAddChapterDialog.value = true;
};

const saveChapter = async () => {
  if (book.value && newChapterTitle.value) {
    await store.addChapter(book.value.id, { title: newChapterTitle.value });
    showAddChapterDialog.value = false;
  }
};
</script>