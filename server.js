import express from 'express';
import cors from 'cors';
import { promises as fs } from 'fs';
import path from 'path';
import QRCode from 'qrcode';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3001;
const BOOKS_FILE = path.join(__dirname, 'public', 'books.json');

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Helper function to read books
async function readBooks() {
  try {
    const data = await fs.readFile(BOOKS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading books:', error);
    return [];
  }
}

// Helper function to write books
async function writeBooks(books) {
  try {
    await fs.writeFile(BOOKS_FILE, JSON.stringify(books, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing books:', error);
    return false;
  }
}

// Get all books
app.get('/api/books', async (req, res) => {
  const books = await readBooks();
  res.json(books);
});

// Add a new book
app.post('/api/books', async (req, res) => {
  try {
    const books = await readBooks();
    const newBook = {
      id: Date.now().toString(),
      title: req.body.title,
      description: req.body.description || '',
      chapters: []
    };
    
    books.push(newBook);
    const success = await writeBooks(books);
    
    if (success) {
      res.json(newBook);
    } else {
      res.status(500).json({ error: 'Failed to save book' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add a chapter to a book
app.post('/api/books/:bookId/chapters', async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);
    
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }
    
    const newChapter = {
      id: Date.now().toString(),
      title: req.body.title,
      items: []
    };
    
    book.chapters.push(newChapter);
    const success = await writeBooks(books);
    
    if (success) {
      res.json(newChapter);
    } else {
      res.status(500).json({ error: 'Failed to save chapter' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add an item to a chapter
app.post('/api/books/:bookId/chapters/:chapterId/items', async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);
    
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }
    
    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }
    
    const newItem = {
      id: Date.now().toString(),
      ...req.body
    };
    
    chapter.items.push(newItem);
    const success = await writeBooks(books);
    
    if (success) {
      res.json(newItem);
    } else {
      res.status(500).json({ error: 'Failed to save item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate QR code for an item
app.get('/api/qr/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();
    
    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }
    
    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }
    
    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }
    
    // Create QR code data with item information
    const qrData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      url: `${req.protocol}://${req.get('host')}/item/${bookId}/${chapterId}/${itemId}`
    };
    
    // Generate QR code
    const qrCodeDataURL = await QRCode.toDataURL(JSON.stringify(qrData), {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: 256
    });
    
    res.json({
      qrCode: qrCodeDataURL,
      data: qrData,
      item: item
    });
    
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate stylized QR code
app.get('/api/qr-styled/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const style = req.query.style || 'default';
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Create QR code data
    const qrData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      url: `${req.protocol}://${req.get('host')}/item/${bookId}/${chapterId}/${itemId}`
    };

    // Define different style options
    const styleOptions = {
      default: {
        errorCorrectionLevel: 'H',
        type: 'image/png',
        quality: 0.92,
        margin: 2,
        color: {
          dark: '#1f2937',
          light: '#f8fafc'
        },
        width: 300
      },
      blue: {
        errorCorrectionLevel: 'H',
        type: 'image/png',
        quality: 0.92,
        margin: 3,
        color: {
          dark: '#1e40af', // Blue
          light: '#eff6ff'
        },
        width: 320
      },
      green: {
        errorCorrectionLevel: 'H',
        type: 'image/png',
        quality: 0.92,
        margin: 3,
        color: {
          dark: '#059669', // Green
          light: '#ecfdf5'
        },
        width: 320
      },
      purple: {
        errorCorrectionLevel: 'H',
        type: 'image/png',
        quality: 0.92,
        margin: 3,
        color: {
          dark: '#7c3aed', // Purple
          light: '#f5f3ff'
        },
        width: 320
      },
      high_contrast: {
        errorCorrectionLevel: 'H',
        type: 'image/png',
        quality: 0.95,
        margin: 4,
        color: {
          dark: '#000000',
          light: '#ffffff'
        },
        width: 350
      }
    };

    const selectedStyle = styleOptions[style] || styleOptions.default;

    // Generate stylized QR code with selected options
    const qrCodeDataURL = await QRCode.toDataURL(JSON.stringify(qrData), selectedStyle);

    res.json({
      qrCode: qrCodeDataURL,
      data: qrData,
      item: item,
      styled: true,
      style: style
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
