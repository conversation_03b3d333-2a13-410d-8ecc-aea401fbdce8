import express from 'express';
import cors from 'cors';
import { promises as fs } from 'fs';
import path from 'path';
import QRCodeStyling from 'qr-code-styling';
import { fileURLToPath } from 'url';
import crypto from 'crypto';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3001;
const BOOKS_FILE = path.join(__dirname, 'public', 'books.json');

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Helper function to read books
async function readBooks() {
  try {
    const data = await fs.readFile(BOOKS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading books:', error);
    return [];
  }
}

// Helper function to write books
async function writeBooks(books) {
  try {
    await fs.writeFile(BOOKS_FILE, JSON.stringify(books, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing books:', error);
    return false;
  }
}

// Generate secure token for QR access
function generateSecureToken() {
  return crypto.randomBytes(32).toString('hex');
}

// Store for QR tokens (in production, use Redis or database)
const qrTokens = new Map();

// Helper function to create smooth QR code with rounded modules
async function createSmoothQRCode(data, itemType, style = 'default') {
  try {
    console.log(`Generating QR code for ${itemType} with style ${style}`);
    const colors = getStyleColors(style);

    const qrCodeOptions = {
      width: 400,
      height: 400,
      data: data,
      dotsOptions: {
        color: colors.dark,
        type: "rounded"
      },
      backgroundOptions: {
        color: colors.light,
      },
      cornersSquareOptions: {
        color: colors.dark,
        type: "extra-rounded"
      },
      cornersDotOptions: {
        color: colors.dark,
        type: "dot"
      }
    };

    // Try to add icon if available
    const iconPath = path.join(__dirname, 'public', 'icons', `${itemType}.svg`);
    try {
      await fs.access(iconPath);
      console.log(`Using icon: ${iconPath}`);
      qrCodeOptions.image = iconPath;
      qrCodeOptions.imageOptions = {
        crossOrigin: "anonymous",
        margin: 20,
        imageSize: 0.3,
        hideBackgroundDots: true
      };
    } catch (error) {
      console.log(`Icon not found for type: ${itemType}, using default`);
    }

    console.log('Creating QRCodeStyling instance...');
    const qrCode = new QRCodeStyling(qrCodeOptions);

    console.log('Getting raw data as buffer...');
    // Use getRawData with nodeCanvas for Node.js environment
    const buffer = await qrCode.getRawData();

    if (!buffer) {
      throw new Error('Failed to generate QR code buffer');
    }

    console.log('QR code generated successfully, buffer size:', buffer.length);
    return buffer;

  } catch (error) {
    console.error('Error in createSmoothQRCode:', error);
    console.error('Error details:', error.message);
    throw new Error(`QR code generation failed: ${error.message}`);
  }
}

// Get style colors
function getStyleColors(style) {
  const styles = {
    default: { dark: '#1f2937', light: '#f8fafc' },
    blue: { dark: '#1e40af', light: '#eff6ff' },
    green: { dark: '#059669', light: '#ecfdf5' },
    purple: { dark: '#7c3aed', light: '#f5f3ff' },
    high_contrast: { dark: '#000000', light: '#ffffff' }
  };
  return styles[style] || styles.default;
}

// Get all books
app.get('/api/books', async (req, res) => {
  const books = await readBooks();
  res.json(books);
});

// Add a new book
app.post('/api/books', async (req, res) => {
  try {
    const books = await readBooks();
    const newBook = {
      id: Date.now().toString(),
      title: req.body.title,
      description: req.body.description || '',
      chapters: []
    };
    
    books.push(newBook);
    const success = await writeBooks(books);
    
    if (success) {
      res.json(newBook);
    } else {
      res.status(500).json({ error: 'Failed to save book' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add a chapter to a book
app.post('/api/books/:bookId/chapters', async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);
    
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }
    
    const newChapter = {
      id: Date.now().toString(),
      title: req.body.title,
      items: []
    };
    
    book.chapters.push(newChapter);
    const success = await writeBooks(books);
    
    if (success) {
      res.json(newChapter);
    } else {
      res.status(500).json({ error: 'Failed to save chapter' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add an item to a chapter
app.post('/api/books/:bookId/chapters/:chapterId/items', async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);
    
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }
    
    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }
    
    const newItem = {
      id: Date.now().toString(),
      ...req.body
    };
    
    chapter.items.push(newItem);
    const success = await writeBooks(books);
    
    if (success) {
      res.json(newItem);
    } else {
      res.status(500).json({ error: 'Failed to save item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate QR code for an item
app.get('/api/qr/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Generate secure token for this item
    const token = generateSecureToken();
    const tokenData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Store token (expires in 24 hours)
    qrTokens.set(token, tokenData);
    setTimeout(() => qrTokens.delete(token), 24 * 60 * 60 * 1000);

    // Create URL that points to our display page
    const displayUrl = `${req.protocol}://${req.get('host')}/display/${token}`;

    // Generate smooth QR code
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, 'default');
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: tokenData,
      item: item,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate stylized QR code
app.get('/api/qr-styled/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const style = req.query.style || 'default';
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }







    // Generate secure token for this item
    const token = generateSecureToken();
    const tokenData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Store token (expires in 24 hours)
    qrTokens.set(token, tokenData);
    setTimeout(() => qrTokens.delete(token), 24 * 60 * 60 * 1000);

    // Create URL that points to our display page
    const displayUrl = `${req.protocol}://${req.get('host')}/display/${token}`;

    // Generate smooth QR code with selected style
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, style);
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: tokenData,
      item: item,
      styled: true,
      style: style,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Display page for QR code access
app.get('/display/:token', async (req, res) => {
  try {
    const { token } = req.params;
    const tokenData = qrTokens.get(token);

    if (!tokenData) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Invalid or Expired Link</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🔒 Access Denied</h1>
            <p>This link is invalid or has expired. QR codes are only valid for 24 hours.</p>
          </div>
        </body>
        </html>
      `);
    }

    const { item, bookTitle, chapterTitle } = tokenData;

    // Handle different item types
    if (item.type === 'link' && item.url) {
      // For links, redirect directly
      return res.redirect(item.url);
    }

    // For other types, show beautiful display page
    const displayPage = generateDisplayPage(item, bookTitle, chapterTitle);
    res.send(displayPage);

  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
});

// Generate beautiful display page
function generateDisplayPage(item, bookTitle, chapterTitle) {
  const getItemIcon = (type) => {
    const icons = {
      question: '❓',
      text: '📄',
      image: '🖼️',
      link: '🔗'
    };
    return icons[type] || '📋';
  };

  const renderItemContent = (item) => {
    switch (item.type) {
      case 'question':
        return `
          <div class="question-container">
            <h2 class="question-title">${item.question || item.title}</h2>
            ${item.options ? `
              <div class="options-container" id="optionsContainer">
                ${item.options.map((option, index) => `
                  <div class="option" data-index="${index}" onclick="selectOption(${index}, ${item.correctAnswer})">
                    <span class="option-letter">${String.fromCharCode(65 + index)}</span>
                    <span class="option-text">${option.text}</span>
                  </div>
                `).join('')}
              </div>
              <div id="result" class="result-container" style="display: none;">
                <div id="resultMessage" class="result-message"></div>
                <button onclick="resetQuestion()" class="reset-button">Try Again</button>
              </div>
            ` : ''}
          </div>
        `;
      case 'text':
        return `
          <div class="text-container">
            <div class="text-content">${item.content || 'No content available'}</div>
          </div>
        `;
      case 'image':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">🖼️</span>
              <p>Image: ${item.url || 'No image URL provided'}</p>
            </div>
          </div>
        `;
      default:
        return `<div class="default-content">Content not available</div>`;
    }
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${item.title} - ${bookTitle}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          padding: 20px;
          line-height: 1.6;
        }

        .container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          border-radius: 20px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          overflow: hidden;
          animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .header {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          padding: 30px;
          text-align: center;
        }

        .item-icon {
          font-size: 3rem;
          margin-bottom: 15px;
          display: block;
        }

        .item-title {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 10px;
        }

        .breadcrumb {
          opacity: 0.9;
          font-size: 0.9rem;
        }

        .content {
          padding: 40px;
        }

        .question-container {
          text-align: center;
        }

        .question-title {
          font-size: 1.5rem;
          color: #1a202c;
          margin-bottom: 30px;
          font-weight: 600;
        }

        .options-container {
          display: grid;
          gap: 15px;
          max-width: 600px;
          margin: 0 auto;
        }

        .option {
          display: flex;
          align-items: center;
          padding: 20px;
          background: #f7fafc;
          border-radius: 12px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s ease;
          position: relative;
          cursor: pointer;
        }

        .option:hover {
          background: #edf2f7;
          border-color: #cbd5e0;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .option.selected {
          background: #ebf8ff;
          border-color: #4299e1;
        }

        .option.correct {
          background: #f0fff4;
          border-color: #68d391;
          box-shadow: 0 4px 12px rgba(104, 211, 145, 0.2);
        }

        .option.incorrect {
          background: #fed7d7;
          border-color: #fc8181;
          box-shadow: 0 4px 12px rgba(252, 129, 129, 0.2);
        }

        .option.disabled {
          cursor: not-allowed;
          opacity: 0.7;
        }

        .option-letter {
          background: #4f46e5;
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          margin-right: 15px;
          flex-shrink: 0;
        }

        .option.correct .option-letter {
          background: #38a169;
        }

        .option.incorrect .option-letter {
          background: #e53e3e;
        }

        .option-text {
          flex: 1;
          font-size: 1.1rem;
        }

        .correct-indicator {
          color: #38a169;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .incorrect-indicator {
          color: #e53e3e;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .result-container {
          margin-top: 30px;
          text-align: center;
          padding: 20px;
          border-radius: 12px;
          animation: fadeIn 0.5s ease-out;
        }

        .result-message {
          font-size: 1.2rem;
          font-weight: 600;
          margin-bottom: 20px;
        }

        .result-message.correct {
          color: #38a169;
        }

        .result-message.incorrect {
          color: #e53e3e;
        }

        .reset-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .reset-button:hover {
          background: #4338ca;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .text-container {
          max-width: 700px;
          margin: 0 auto;
        }

        .text-content {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #2d3748;
          background: #f7fafc;
          padding: 30px;
          border-radius: 12px;
          border-left: 4px solid #4f46e5;
        }

        .image-container {
          text-align: center;
        }

        .image-placeholder {
          background: #f7fafc;
          border: 2px dashed #cbd5e0;
          border-radius: 12px;
          padding: 60px 30px;
          color: #718096;
        }

        .image-icon {
          font-size: 4rem;
          display: block;
          margin-bottom: 20px;
        }

        .footer {
          background: #f7fafc;
          padding: 20px 40px;
          text-align: center;
          color: #718096;
          font-size: 0.9rem;
          border-top: 1px solid #e2e8f0;
        }

        @media (max-width: 768px) {
          body { padding: 10px; }
          .container { border-radius: 15px; }
          .header { padding: 20px; }
          .item-title { font-size: 1.5rem; }
          .content { padding: 20px; }
          .question-title { font-size: 1.3rem; }
          .option { padding: 15px; }
          .option-text { font-size: 1rem; }
          .text-content { font-size: 1.1rem; padding: 20px; }
        }
      </style>
      <script>
        function selectOption(selectedIndex, correctAnswer) {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');
          const resultMessage = document.getElementById('resultMessage');

          // Disable all options
          options.forEach(option => {
            option.classList.add('disabled');
            option.onclick = null;
          });

          // Mark selected option
          options[selectedIndex].classList.add('selected');

          // Show correct/incorrect styling
          if (selectedIndex === correctAnswer) {
            options[selectedIndex].classList.add('correct');
            options[selectedIndex].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '🎉 Correct! Well done!';
            resultMessage.className = 'result-message correct';
          } else {
            options[selectedIndex].classList.add('incorrect');
            options[selectedIndex].innerHTML += '<span class="incorrect-indicator">✗</span>';
            options[correctAnswer].classList.add('correct');
            options[correctAnswer].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '❌ Incorrect. The correct answer is highlighted.';
            resultMessage.className = 'result-message incorrect';
          }

          // Show result
          resultContainer.style.display = 'block';
        }

        function resetQuestion() {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');

          // Reset all options
          options.forEach((option, index) => {
            option.className = 'option';
            option.onclick = () => selectOption(index, ${item.correctAnswer || 0});
            // Remove indicators
            const indicators = option.querySelectorAll('.correct-indicator, .incorrect-indicator');
            indicators.forEach(indicator => indicator.remove());
          });

          // Hide result
          resultContainer.style.display = 'none';
        }
      </script>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <span class="item-icon">${getItemIcon(item.type)}</span>
          <h1 class="item-title">${item.title}</h1>
          <div class="breadcrumb">${bookTitle} → ${chapterTitle}</div>
        </div>

        <div class="content">
          ${renderItemContent(item)}
        </div>

        <div class="footer">
          <p>📚 Accessed via QR Code • ${new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

console.log('Starting server...');

app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log(`🌐 Network access: http://[your-ip]:${PORT}`);
  console.log(`📚 API available at: http://localhost:${PORT}/api/books`);
});
